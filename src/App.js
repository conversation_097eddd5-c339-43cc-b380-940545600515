import React, { useState, useEffect, useRef } from 'react';

// --- Helper Data & Components ---

// Mapping of event categories to their visual styles and icons for both light and dark modes
const getCategoryStyles = (theme) => ({
    'Research': {
        bg: theme === 'dark' ? 'bg-sky-900' : 'bg-sky-100',
        text: theme === 'dark' ? 'text-sky-300' : 'text-sky-800',
        icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
            </svg>
        )
    },
    'Launch': {
        bg: theme === 'dark' ? 'bg-emerald-900' : 'bg-emerald-100',
        text: theme === 'dark' ? 'text-emerald-300' : 'text-emerald-800',
        icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
        )
    },
    'Acquisition': {
        bg: theme === 'dark' ? 'bg-purple-900' : 'bg-purple-100',
        text: theme === 'dark' ? 'text-purple-300' : 'text-purple-800',
        icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v.01M12 6v-1m0-1V4m0 2.01M12 14c-1.11 0-2.08-.402-2.599-1M12 14v1m0 1v1m0-2.01M4.6 11a6 6 0 0114.8 0M12 18a6 6 0 01-6-6h12a6 6 0 01-6 6z"></path>
            </svg>
        )
    },
    'Milestone': {
        bg: theme === 'dark' ? 'bg-amber-900' : 'bg-amber-100',
        text: theme === 'dark' ? 'text-amber-300' : 'text-amber-800',
        icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
            </svg>
        )
    }
});

// --- Vertical Timeline Components ---

const VerticalTimelineItem = ({ event, index, theme }) => {
    const { date, category, title, description } = event;
    const styles = getCategoryStyles(theme);
    const style = styles[category] || styles['Milestone'];
    const isLeft = index % 2 === 0;

    const Card = () => (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm w-full">
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">{date}</p>
            <div className={`inline-flex items-center gap-x-2 px-3 py-1 rounded-full text-sm font-medium ${style.bg} ${style.text} mb-3`}>
                {style.icon}
                {category}
            </div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">{title}</h3>
            <p className="text-gray-600 dark:text-gray-300 text-base">{description}</p>
        </div>
    );

    const timelineContent = (
        <>
            <div className="order-1 w-5/12"></div>
            <div className="z-20 flex items-center order-1 bg-gray-700 dark:bg-gray-600 shadow-xl w-8 h-8 rounded-full">
                <h1 className="mx-auto font-semibold text-lg text-white">{index + 1}</h1>
            </div>
            <div className="order-1 w-5/12"><Card /></div>
        </>
    );

    return (
        <div className={`mb-8 flex justify-between items-center w-full ${isLeft ? '' : 'flex-row-reverse'}`}>
            {timelineContent}
        </div>
    );
};

const MobileVerticalTimelineItem = ({ event, theme }) => {
    const { date, category, title, description } = event;
    const styles = getCategoryStyles(theme);
    const style = styles[category] || styles['Milestone'];

    return (
        <div className="relative pl-8 sm:pl-32 py-6 group">
             <div className="flex flex-col sm:flex-row items-start mb-1 group-last:before:hidden before:absolute before:left-2 sm:before:left-0 before:h-full before:px-px before:bg-slate-200 dark:before:bg-slate-700 sm:before:ml-[6.5rem] before:self-start before:-translate-x-1/2 before:translate-y-3 after:absolute after:left-2 sm:after:left-0 after:w-2 after:h-2 after:bg-indigo-600 after:border-4 after:box-content after:border-slate-50 dark:after:border-gray-900 after:rounded-full sm:after:ml-[6.5rem] after:-translate-x-1/2 after:translate-y-1.5">
                <time className="sm:absolute left-0 translate-y-0.5 inline-flex items-center justify-center text-xs font-semibold uppercase w-20 h-6 mb-3 sm:mb-0 text-emerald-800 dark:text-emerald-200 bg-emerald-100 dark:bg-emerald-900 rounded-full">{date}</time>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm w-full">
                    <div className={`inline-flex items-center gap-x-2 px-3 py-1 rounded-full text-sm font-medium ${style.bg} ${style.text} mb-3`}>
                        {style.icon}
                        {category}
                    </div>
                    <h3 className="font-bold text-gray-800 dark:text-gray-100 text-lg mb-1">{title}</h3>
                    <p className="text-gray-600 dark:text-gray-300">{description}</p>
                </div>
            </div>
        </div>
    );
};

const VerticalTimeline = ({ data, theme }) => (
    <>
        <div className="relative wrap overflow-hidden p-10 h-full hidden md:block">
            <div className="border-2-2 absolute border-opacity-20 border-gray-700 dark:border-gray-500 h-full border" style={{left: '50%'}}></div>
            {data.map((event, index) => (
                <VerticalTimelineItem key={index} event={event} index={index} theme={theme} />
            ))}
        </div>
        <div className="block md:hidden">
            <div className="space-y-4">
                 {data.map((event, index) => (
                    <MobileVerticalTimelineItem key={index} event={event} theme={theme} />
                ))}
            </div>
        </div>
    </>
);


// --- Horizontal Timeline Components ---

const HorizontalTimelineItem = ({ event, theme }) => {
    const { date, category, title, description } = event;
    const styles = getCategoryStyles(theme);
    const style = styles[category] || styles['Milestone'];

    return (
        <div className="relative flex-shrink-0 w-80 md:w-96 p-4 pt-8">
            <div className="absolute top-0 left-1/2 w-0.5 h-8 bg-gray-300 dark:bg-gray-600"></div>
            <div className="absolute top-8 left-1/2 w-4 h-4 -ml-2 bg-white dark:bg-gray-700 border-2 border-indigo-500 rounded-full"></div>
            <div className="mt-10 bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm h-full flex flex-col">
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">{date}</p>
                <div className={`inline-flex items-center gap-x-2 px-3 py-1 rounded-full text-sm font-medium ${style.bg} ${style.text} mb-3 self-start`}>
                    {style.icon}
                    {category}
                </div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">{title}</h3>
                <p className="text-gray-600 dark:text-gray-300 text-base flex-grow">{description}</p>
            </div>
        </div>
    );
};

const HorizontalTimeline = ({ data, theme }) => {
    const scrollContainerRef = useRef(null);
    const [isDragging, setIsDragging] = useState(false);
    const [startX, setStartX] = useState(0);
    const [scrollLeft, setScrollLeft] = useState(0);
    const [canScrollLeft, setCanScrollLeft] = useState(false);
    const [canScrollRight, setCanScrollRight] = useState(true);

    const checkScrollability = () => {
        const el = scrollContainerRef.current;
        if (el) {
            setCanScrollLeft(el.scrollLeft > 0);
            setCanScrollRight(el.scrollLeft < el.scrollWidth - el.clientWidth - 1); // -1 for precision
        }
    };

    useEffect(() => {
        const el = scrollContainerRef.current;
        if (el) {
            checkScrollability();
            el.addEventListener('scroll', checkScrollability);
        }
        return () => {
            if (el) {
                el.removeEventListener('scroll', checkScrollability);
            }
        };
    }, [data]);

    const scroll = (direction) => {
        if (scrollContainerRef.current) {
            const scrollAmount = scrollContainerRef.current.clientWidth * 0.8;
            scrollContainerRef.current.scrollBy({
                left: direction === 'left' ? -scrollAmount : scrollAmount,
                behavior: 'smooth'
            });
        }
    };

    const onMouseDown = (e) => {
        if (!scrollContainerRef.current) return;
        setIsDragging(true);
        setStartX(e.pageX - scrollContainerRef.current.offsetLeft);
        setScrollLeft(scrollContainerRef.current.scrollLeft);
        scrollContainerRef.current.style.cursor = 'grabbing';
    };

    const onMouseLeaveOrUp = () => {
        if (!scrollContainerRef.current) return;
        setIsDragging(false);
        scrollContainerRef.current.style.cursor = 'grab';
    };

    const onMouseMove = (e) => {
        if (!isDragging || !scrollContainerRef.current) return;
        e.preventDefault();
        const x = e.pageX - scrollContainerRef.current.offsetLeft;
        const walk = (x - startX) * 2; // The multiplier 2 makes scrolling faster
        scrollContainerRef.current.scrollLeft = scrollLeft - walk;
    };

    return (
        <div className="relative w-full group">
            <div
                ref={scrollContainerRef}
                onMouseDown={onMouseDown}
                onMouseLeave={onMouseLeaveOrUp}
                onMouseUp={onMouseLeaveOrUp}
                onMouseMove={onMouseMove}
                className="flex overflow-x-auto pb-8 scrollbar-hide cursor-grab"
                style={{paddingTop: '2.5rem'}}
            >
                <div className="absolute top-1/2 left-0 w-full h-0.5 bg-gray-300 dark:bg-gray-600" style={{top: '4.5rem', transform: 'translateY(-50%)'}}></div>
                {data.map((event, index) => (
                    <HorizontalTimelineItem key={index} event={event} theme={theme} />
                ))}
            </div>

            {canScrollLeft && (
                <button
                    onClick={() => scroll('left')}
                    className="absolute top-1/2 left-0 -translate-y-1/2 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-full w-10 h-10 flex items-center justify-center shadow-md hover:bg-white dark:hover:bg-gray-800 transition-opacity duration-300 opacity-0 group-hover:opacity-100"
                >
                    <svg className="w-6 h-6 text-gray-700 dark:text-gray-200" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path></svg>
                </button>
            )}

            {canScrollRight && (
                <button
                    onClick={() => scroll('right')}
                    className="absolute top-1/2 right-0 -translate-y-1/2 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-full w-10 h-10 flex items-center justify-center shadow-md hover:bg-white dark:hover:bg-gray-800 transition-opacity duration-300 opacity-0 group-hover:opacity-100"
                >
                    <svg className="w-6 h-6 text-gray-700 dark:text-gray-200" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path></svg>
                </button>
            )}
            
            <div className={`absolute top-0 left-0 h-full w-16 bg-gradient-to-r from-gray-100 dark:from-gray-900 to-transparent pointer-events-none transition-opacity duration-300 ${canScrollLeft ? 'opacity-100' : 'opacity-0'}`}></div>
            <div className={`absolute top-0 right-0 h-full w-16 bg-gradient-to-l from-gray-100 dark:from-gray-900 to-transparent pointer-events-none transition-opacity duration-300 ${canScrollRight ? 'opacity-100' : 'opacity-0'}`}></div>
        </div>
    );
};


// --- UI Controls ---

const Controls = ({ theme, setTheme, layout, setLayout }) => {
    const toggleTheme = () => setTheme(theme === 'light' ? 'dark' : 'light');

    return (
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
            {/* Layout Toggle */}
            <div className="flex items-center p-1 bg-gray-200 dark:bg-gray-700 rounded-lg">
                <button onClick={() => setLayout('vertical')} className={`px-4 py-2 text-sm font-medium rounded-md ${layout === 'vertical' ? 'bg-white dark:bg-gray-900 text-indigo-600 dark:text-indigo-400 shadow' : 'text-gray-600 dark:text-gray-300'}`}>
                    Vertical
                </button>
                <button onClick={() => setLayout('horizontal')} className={`px-4 py-2 text-sm font-medium rounded-md ${layout === 'horizontal' ? 'bg-white dark:bg-gray-900 text-indigo-600 dark:text-indigo-400 shadow' : 'text-gray-600 dark:text-gray-300'}`}>
                    Horizontal
                </button>
            </div>
            {/* Theme Toggle */}
            <div className="flex items-center">
                <span className="mr-2 text-sm text-gray-600 dark:text-gray-400">Light</span>
                <button onClick={toggleTheme} className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${theme === 'dark' ? 'bg-indigo-600' : 'bg-gray-300'}`}>
                    <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${theme === 'dark' ? 'translate-x-6' : 'translate-x-1'}`} />
                </button>
                <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Dark</span>
            </div>
        </div>
    );
};


// --- Main App Component ---

export default function App() {
    const [theme, setTheme] = useState('light');
    const [layout, setLayout] = useState('vertical');
    
    useEffect(() => {
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }, [theme]);

    const timelineData = [
        { date: '2014', category: 'Research', title: 'The GAN is Born', description: 'Ian Goodfellow publishes the paper on Generative Adversarial Networks, sparking a revolution in AI image generation.' },
        { date: '2017', category: 'Research', title: 'Attention Is All You Need', description: 'Google researchers publish the paper on the Transformer architecture, the fundamental building block for modern LLMs.' },
        { date: '2020', category: 'Launch', title: 'GPT-3 Stuns Researchers', description: 'OpenAI releases GPT-3, a massive leap in capability that demonstrates the power of scale in language models.' },
        { date: '2022', category: 'Milestone', title: 'The Rise of AI Art', description: 'Public access to high-quality AI image generators like DALL-E 2 and Midjourney creates a cultural moment.' },
        { date: '2022', category: 'Launch', title: 'ChatGPT Goes Public', description: 'OpenAI packages its technology into a simple, conversational interface, leading to explosive public adoption.' },
        { date: '2023', category: 'Acquisition', title: 'The AI Arms Race', description: 'Competition intensifies as major tech companies like Google and startups like Anthropic launch competing models.' },
        { date: '2024', category: 'Milestone', title: 'Video Generation Models', description: 'AI models like Sora demonstrate the ability to generate high-fidelity video from text prompts, opening new creative avenues.' },
    ];

    return (
        <div className="bg-gray-100 dark:bg-gray-900 min-h-screen font-sans antialiased transition-colors duration-300">
            <div className="container mx-auto px-4 py-12">
                <header className="text-center mb-8">
                    <h1 className="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white">The Evolution of Generative AI</h1>
                    <p className="text-lg text-gray-600 dark:text-gray-400 mt-4 max-w-3xl mx-auto">
                        A timeline of key moments, from foundational research papers to the products that brought AI into the mainstream.
                    </p>
                </header>

                <Controls theme={theme} setTheme={setTheme} layout={layout} setLayout={setLayout} />

                {layout === 'vertical' ? (
                    <VerticalTimeline data={timelineData} theme={theme} />
                ) : (
                    <HorizontalTimeline data={timelineData} theme={theme} />
                )}
            </div>
        </div>
    );
}
